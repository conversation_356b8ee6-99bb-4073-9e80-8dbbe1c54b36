package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// Contract 合同主表 - 对应 tbl_contracts
type Contract struct {
	BaseModel
	ContractCode      string          `json:"contract_code" gorm:"type:varchar(50);unique;not null" validate:"required,max=50"`
	ContractName      string          `json:"contract_name" gorm:"type:varchar(255);not null" validate:"required,max=255"`
	ContractType      string          `json:"contract_type" gorm:"type:varchar(50);not null" validate:"required,max=50"`
	CounterpartyID    *uuid.UUID      `json:"counterparty_id,omitempty" gorm:"type:uuid"`
	CounterpartyName  string          `json:"counterparty_name" gorm:"type:varchar(255);not null" validate:"required,max=255"`
	TotalAmount       decimal.Decimal `json:"total_amount" gorm:"type:decimal(18,2);not null" validate:"required,gte=0"`
	StartDate         *time.Time      `json:"start_date,omitempty" gorm:"type:date"`
	EndDate           *time.Time      `json:"end_date,omitempty" gorm:"type:date"`
	Status            string          `json:"status" gorm:"type:varchar(30);not null" validate:"required,max=30"`

	// 关联关系
	Counterparty    *Supplier                   `json:"counterparty,omitempty" gorm:"foreignKey:CounterpartyID"`
	PaymentSchedules []ContractPaymentSchedule  `json:"payment_schedules,omitempty" gorm:"foreignKey:ContractID"`
}

// TableName 指定表名
func (Contract) TableName() string {
	return "tbl_contracts"
}

// ContractPaymentSchedule 合同分期付款计划 - 对应 tbl_contract_payment_schedules
type ContractPaymentSchedule struct {
	ID         uuid.UUID       `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ContractID uuid.UUID       `json:"contract_id" gorm:"type:uuid;not null" validate:"required"`
	PhaseName  *string         `json:"phase_name,omitempty" gorm:"type:varchar(100)"`
	DueDate    *time.Time      `json:"due_date,omitempty" gorm:"type:date"`
	Amount     decimal.Decimal `json:"amount" gorm:"type:decimal(18,2);not null" validate:"required,gte=0"`
	Status     string          `json:"status" gorm:"type:varchar(30);not null" validate:"required,max=30"`
	PaymentID  *uuid.UUID      `json:"payment_id,omitempty" gorm:"type:uuid"`
	CreatedAt  time.Time       `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt  time.Time       `json:"updated_at" gorm:"not null;default:CURRENT_TIMESTAMP"`

	// 关联关系
	Contract *Contract `json:"contract,omitempty" gorm:"foreignKey:ContractID"`
	Payment  *Payment  `json:"payment,omitempty" gorm:"foreignKey:PaymentID"`
}

// TableName 指定表名
func (ContractPaymentSchedule) TableName() string {
	return "tbl_contract_payment_schedules"
}
