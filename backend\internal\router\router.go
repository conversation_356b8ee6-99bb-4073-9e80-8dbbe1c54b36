package router

import (
	"hospital-management/internal/config"
	"hospital-management/internal/middleware"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"gorm.io/gorm"
)

func Setup(db *gorm.DB, cfg *config.Config) *gin.Engine {
	r := gin.Default()

	// 中间件
	r.Use(middleware.CORS())
	r.Use(middleware.Logger())
	r.Use(middleware.Recovery())

	// Swagger文档
	r.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"message": "Hospital Management System API is running",
		})
	})

	// API路由组
	api := r.Group("/api/v1")
	{
		// 认证相关
		auth := api.Group("/auth")
		{
			auth.POST("/login", func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "Login endpoint - TODO"})
			})
			auth.POST("/logout", func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "Logout endpoint - TODO"})
			})
		}

		// 需要认证的路由
		protected := api.Group("/")
		protected.Use(middleware.JWTAuth(cfg.JWTSecret))
		{
			// 用户管理
			users := protected.Group("/users")
			{
				users.GET("", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Get users - TODO"})
				})
				users.POST("", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Create user - TODO"})
				})
				users.GET("/:id", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Get user by ID - TODO"})
				})
				users.PUT("/:id", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Update user - TODO"})
				})
				users.DELETE("/:id", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Delete user - TODO"})
				})
			}

			// 部门管理
			departments := protected.Group("/departments")
			{
				departments.GET("", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Get departments - TODO"})
				})
				departments.POST("", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Create department - TODO"})
				})
			}

			// 预算管理
			budget := protected.Group("/budget")
			{
				budget.GET("/schemes", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Get budget schemes - TODO"})
				})
				budget.POST("/schemes", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Create budget scheme - TODO"})
				})
			}

			// 报销管理
			expense := protected.Group("/expense")
			{
				expense.GET("/applications", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Get expense applications - TODO"})
				})
				expense.POST("/applications", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Create expense application - TODO"})
				})
			}

			// 审批管理
			approval := protected.Group("/approval")
			{
				approval.GET("/todo", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Get todo approvals - TODO"})
				})
				approval.POST("/approve/:id", func(c *gin.Context) {
					c.JSON(200, gin.H{"message": "Approve - TODO"})
				})
			}
		}
	}

	return r
}
