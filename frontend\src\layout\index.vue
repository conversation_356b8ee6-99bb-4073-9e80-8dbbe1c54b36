<template>
  <a-layout class="layout">
    <!-- 侧边栏 -->
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      class="sider"
    >
      <div class="logo">
        <h3 v-if="!collapsed">医院管理系统</h3>
        <h3 v-else>HMS</h3>
      </div>
      
      <a-menu
        v-model:selectedKeys="selectedKeys"
        v-model:openKeys="openKeys"
        mode="inline"
        theme="dark"
        :items="menuItems"
        @click="handleMenuClick"
      />
    </a-layout-sider>
    
    <!-- 主内容区 -->
    <a-layout>
      <!-- 顶部导航 -->
      <a-layout-header class="header">
        <div class="header-left">
          <a-button
            type="text"
            :icon="collapsed ? h(MenuUnfoldOutlined) : h(MenuFoldOutlined)"
            @click="() => (collapsed = !collapsed)"
            class="trigger"
          />
          
          <a-breadcrumb class="breadcrumb">
            <a-breadcrumb-item v-for="item in breadcrumbItems" :key="item.path">
              {{ item.title }}
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
        
        <div class="header-right">
          <a-badge :count="5" class="notification">
            <a-button type="text" :icon="h(BellOutlined)" />
          </a-badge>
          
          <a-dropdown>
            <a-button type="text" class="user-info">
              <a-avatar size="small" :icon="h(UserOutlined)" />
              <span class="username">管理员</span>
              <DownOutlined />
            </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item key="profile">
                  <UserOutlined />
                  个人信息
                </a-menu-item>
                <a-menu-item key="settings">
                  <SettingOutlined />
                  系统设置
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout" @click="handleLogout">
                  <LogoutOutlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>
      
      <!-- 内容区域 -->
      <a-layout-content class="content">
        <router-view />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, computed, h, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BellOutlined,
  UserOutlined,
  DownOutlined,
  SettingOutlined,
  LogoutOutlined,
  DashboardOutlined,
  TeamOutlined,
  DollarOutlined,
  ShoppingOutlined,
  FileTextOutlined,
  DatabaseOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

const router = useRouter()
const route = useRoute()

const collapsed = ref(false)
const selectedKeys = ref<string[]>([])
const openKeys = ref<string[]>([])

// 菜单配置
const menuItems = [
  {
    key: '/dashboard',
    icon: h(DashboardOutlined),
    label: '工作台',
    title: '工作台'
  },
  {
    key: 'system',
    icon: h(TeamOutlined),
    label: '系统管理',
    title: '系统管理',
    children: [
      {
        key: '/system/departments',
        label: '部门管理',
        title: '部门管理'
      },
      {
        key: '/system/users',
        label: '用户管理',
        title: '用户管理'
      },
      {
        key: '/system/roles',
        label: '角色管理',
        title: '角色管理'
      }
    ]
  },
  {
    key: 'budget',
    icon: h(DollarOutlined),
    label: '预算管理',
    title: '预算管理',
    children: [
      {
        key: '/budget/schemes',
        label: '预算方案',
        title: '预算方案'
      },
      {
        key: '/budget/subjects',
        label: '预算科目',
        title: '预算科目'
      },
      {
        key: '/budget/items',
        label: '预算明细',
        title: '预算明细'
      }
    ]
  },
  {
    key: 'expense',
    icon: h(DollarOutlined),
    label: '支出控制',
    title: '支出控制',
    children: [
      {
        key: '/expense/pre-applications',
        label: '事前申请',
        title: '事前申请'
      },
      {
        key: '/expense/applications',
        label: '费用报销',
        title: '费用报销'
      },
      {
        key: '/expense/approvals',
        label: '审批工作台',
        title: '审批工作台'
      },
      {
        key: '/expense/payments',
        label: '付款管理',
        title: '付款管理'
      }
    ]
  },
  {
    key: 'procurement',
    icon: h(ShoppingOutlined),
    label: '采购管理',
    title: '采购管理',
    children: [
      {
        key: '/procurement/suppliers',
        label: '供应商管理',
        title: '供应商管理'
      },
      {
        key: '/procurement/requisitions',
        label: '采购申请',
        title: '采购申请'
      }
    ]
  },
  {
    key: 'contract',
    icon: h(FileTextOutlined),
    label: '合同管理',
    title: '合同管理',
    children: [
      {
        key: '/contract/contracts',
        label: '合同台账',
        title: '合同台账'
      },
      {
        key: '/contract/payment-schedules',
        label: '付款计划',
        title: '付款计划'
      }
    ]
  },
  {
    key: 'asset',
    icon: h(DatabaseOutlined),
    label: '资产管理',
    title: '资产管理',
    children: [
      {
        key: '/asset/categories',
        label: '资产分类',
        title: '资产分类'
      },
      {
        key: '/asset/assets',
        label: '资产台账',
        title: '资产台账'
      }
    ]
  }
]

// 面包屑导航
const breadcrumbItems = computed(() => {
  const items = []
  const pathSegments = route.path.split('/').filter(Boolean)
  
  // 查找当前路径对应的菜单项
  const findMenuItem = (items: any[], path: string): any => {
    for (const item of items) {
      if (item.key === path) {
        return item
      }
      if (item.children) {
        const found = findMenuItem(item.children, path)
        if (found) {
          return found
        }
      }
    }
    return null
  }
  
  const currentItem = findMenuItem(menuItems, route.path)
  if (currentItem) {
    // 查找父级菜单
    const findParent = (items: any[], targetKey: string): any => {
      for (const item of items) {
        if (item.children) {
          const found = item.children.find((child: any) => child.key === targetKey)
          if (found) {
            return item
          }
        }
      }
      return null
    }
    
    const parent = findParent(menuItems, route.path)
    if (parent) {
      items.push({ title: parent.title, path: parent.key })
    }
    items.push({ title: currentItem.title, path: currentItem.key })
  }
  
  return items
})

// 处理菜单点击
const handleMenuClick = ({ key }: { key: string }) => {
  if (key.startsWith('/')) {
    router.push(key)
  }
}

// 处理退出登录
const handleLogout = () => {
  localStorage.removeItem('token')
  message.success('已退出登录')
  router.push('/login')
}

// 监听路由变化，更新选中的菜单
watch(
  () => route.path,
  (newPath) => {
    selectedKeys.value = [newPath]
    
    // 设置展开的菜单
    const pathSegments = newPath.split('/').filter(Boolean)
    if (pathSegments.length > 1) {
      openKeys.value = [pathSegments[0]]
    }
  },
  { immediate: true }
)
</script>

<style scoped>
.layout {
  min-height: 100vh;
}

.sider {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  margin: 16px;
  border-radius: 6px;
}

.logo h3 {
  color: white;
  margin: 0;
  font-size: 16px;
}

.header {
  background: white;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  margin-left: 200px;
  position: fixed;
  top: 0;
  right: 0;
  z-index: 99;
}

.header-left {
  display: flex;
  align-items: center;
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #1890ff;
}

.breadcrumb {
  margin-left: 16px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notification {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.username {
  margin-left: 8px;
}

.content {
  margin-left: 200px;
  margin-top: 64px;
  padding: 24px;
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
}

/* 响应式处理 */
@media (max-width: 768px) {
  .header {
    margin-left: 80px;
  }
  
  .content {
    margin-left: 80px;
  }
}
</style>
