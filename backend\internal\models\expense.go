package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// JSONMap 用于存储JSON数据
type JSONMap map[string]interface{}

// Value 实现 driver.Valuer 接口
func (j JSONMap) Value() (driver.Value, error) {
	return json.Marshal(j)
}

// Scan 实现 sql.Scanner 接口
func (j *JSONMap) Scan(value interface{}) error {
	if value == nil {
		*j = make(JSONMap)
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into JSONMap", value)
	}
	
	return json.Unmarshal(bytes, j)
}

// PreApplication 事前申请单 - 对应 tbl_pre_applications
type PreApplication struct {
	BaseModel
	ApplicationCode string          `json:"application_code" gorm:"type:varchar(50);unique;not null" validate:"required,max=50"`
	ApplicantID     uuid.UUID       `json:"applicant_id" gorm:"type:uuid;not null" validate:"required"`
	DepartmentID    uuid.UUID       `json:"department_id" gorm:"type:uuid;not null" validate:"required"`
	Type            string          `json:"type" gorm:"type:varchar(50);not null" validate:"required,max=50"`
	Title           string          `json:"title" gorm:"type:varchar(255);not null" validate:"required,max=255"`
	EstimatedAmount decimal.Decimal `json:"estimated_amount" gorm:"type:decimal(18,2);not null" validate:"required,gte=0"`
	Status          string          `json:"status" gorm:"type:varchar(30);not null" validate:"required,max=30"`
	Details         *JSONMap        `json:"details,omitempty" gorm:"type:jsonb"`

	// 关联关系
	Applicant  *User       `json:"applicant,omitempty" gorm:"foreignKey:ApplicantID"`
	Department *Department `json:"department,omitempty" gorm:"foreignKey:DepartmentID"`
}

// TableName 指定表名
func (PreApplication) TableName() string {
	return "tbl_pre_applications"
}

// ExpenseApplication 报销申请单主表 - 对应 tbl_expense_applications
type ExpenseApplication struct {
	BaseModel
	ApplicationCode   string          `json:"application_code" gorm:"type:varchar(50);unique;not null" validate:"required,max=50"`
	ApplicantID       uuid.UUID       `json:"applicant_id" gorm:"type:uuid;not null" validate:"required"`
	DepartmentID      uuid.UUID       `json:"department_id" gorm:"type:uuid;not null" validate:"required"`
	PreApplicationID  *uuid.UUID      `json:"pre_application_id,omitempty" gorm:"type:uuid"`
	Title             string          `json:"title" gorm:"type:varchar(255);not null" validate:"required,max=255"`
	TotalAmount       decimal.Decimal `json:"total_amount" gorm:"type:decimal(18,2);not null" validate:"required,gte=0"`
	Status            string          `json:"status" gorm:"type:varchar(30);not null" validate:"required,max=30"`

	// 关联关系
	Applicant      *User              `json:"applicant,omitempty" gorm:"foreignKey:ApplicantID"`
	Department     *Department        `json:"department,omitempty" gorm:"foreignKey:DepartmentID"`
	PreApplication *PreApplication    `json:"pre_application,omitempty" gorm:"foreignKey:PreApplicationID"`
	Details        []ExpenseDetail    `json:"details,omitempty" gorm:"foreignKey:ApplicationID"`
}

// TableName 指定表名
func (ExpenseApplication) TableName() string {
	return "tbl_expense_applications"
}

// ExpenseDetail 报销明细表 - 对应 tbl_expense_details
type ExpenseDetail struct {
	ID            uuid.UUID       `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ApplicationID uuid.UUID       `json:"application_id" gorm:"type:uuid;not null" validate:"required"`
	BudgetItemID  uuid.UUID       `json:"budget_item_id" gorm:"type:uuid;not null" validate:"required"`
	Description   string          `json:"description" gorm:"type:text;not null" validate:"required"`
	Amount        decimal.Decimal `json:"amount" gorm:"type:decimal(18,2);not null" validate:"required,gte=0"`
	ExpenseDate   time.Time       `json:"expense_date" gorm:"type:date;not null" validate:"required"`
	InvoiceInfo   *JSONMap        `json:"invoice_info,omitempty" gorm:"type:jsonb"`
	CreatedAt     time.Time       `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt     time.Time       `json:"updated_at" gorm:"not null;default:CURRENT_TIMESTAMP"`

	// 关联关系
	Application *ExpenseApplication `json:"application,omitempty" gorm:"foreignKey:ApplicationID"`
	BudgetItem  *BudgetItem         `json:"budget_item,omitempty" gorm:"foreignKey:BudgetItemID"`
}

// TableName 指定表名
func (ExpenseDetail) TableName() string {
	return "tbl_expense_details"
}
