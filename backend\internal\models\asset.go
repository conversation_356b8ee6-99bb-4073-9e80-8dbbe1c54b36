package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// AssetCategory 资产分类表 - 对应 tbl_asset_categories
type AssetCategory struct {
	ID        uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ParentID  *uuid.UUID `json:"parent_id,omitempty" gorm:"type:uuid"`
	Name      string     `json:"name" gorm:"type:varchar(100);not null" validate:"required,max=100"`
	Code      string     `json:"code" gorm:"type:varchar(50);unique;not null" validate:"required,max=50"`
	CreatedAt time.Time  `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt time.Time  `json:"updated_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	DeletedAt *time.Time `json:"deleted_at,omitempty" gorm:"type:timestamptz"`

	// 关联关系
	Parent   *AssetCategory  `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	Children []AssetCategory `json:"children,omitempty" gorm:"foreignKey:ParentID"`
	Assets   []Asset         `json:"assets,omitempty" gorm:"foreignKey:CategoryID"`
}

// TableName 指定表名
func (AssetCategory) TableName() string {
	return "tbl_asset_categories"
}

// Asset 资产卡片表 - 对应 tbl_assets
type Asset struct {
	BaseModel
	AssetCode           string           `json:"asset_code" gorm:"type:varchar(50);unique;not null" validate:"required,max=50"`
	AssetName           string           `json:"asset_name" gorm:"type:varchar(255);not null" validate:"required,max=255"`
	CategoryID          uuid.UUID        `json:"category_id" gorm:"type:uuid;not null" validate:"required"`
	SourceType          *string          `json:"source_type,omitempty" gorm:"type:varchar(30)"`
	PurchaseContractID  *uuid.UUID       `json:"purchase_contract_id,omitempty" gorm:"type:uuid"`
	PurchaseDate        *time.Time       `json:"purchase_date,omitempty" gorm:"type:date"`
	PurchasePrice       *decimal.Decimal `json:"purchase_price,omitempty" gorm:"type:decimal(18,2)"`
	Status              string           `json:"status" gorm:"type:varchar(30);not null" validate:"required,max=30"`
	OwnerDeptID         uuid.UUID        `json:"owner_dept_id" gorm:"type:uuid;not null" validate:"required"`
	CustodianID         *uuid.UUID       `json:"custodian_id,omitempty" gorm:"type:uuid"`
	Location            *string          `json:"location,omitempty" gorm:"type:varchar(255)"`

	// 关联关系
	Category        *AssetCategory `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
	PurchaseContract *Contract     `json:"purchase_contract,omitempty" gorm:"foreignKey:PurchaseContractID"`
	OwnerDept       *Department    `json:"owner_dept,omitempty" gorm:"foreignKey:OwnerDeptID"`
	Custodian       *User          `json:"custodian,omitempty" gorm:"foreignKey:CustodianID"`
}

// TableName 指定表名
func (Asset) TableName() string {
	return "tbl_assets"
}
