import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: {
      title: '工作台',
      requiresAuth: true
    }
  },
  {
    path: '/layout',
    component: () => import('@/layout/index.vue'),
    meta: {
      requiresAuth: true
    },
    children: [
      // 组织权限管理
      {
        path: '/system',
        name: 'System',
        meta: { title: '系统管理' },
        children: [
          {
            path: '/system/departments',
            name: 'Departments',
            component: () => import('@/views/system/departments/index.vue'),
            meta: { title: '部门管理' }
          },
          {
            path: '/system/users',
            name: 'Users',
            component: () => import('@/views/system/users/index.vue'),
            meta: { title: '用户管理' }
          },
          {
            path: '/system/roles',
            name: 'Roles',
            component: () => import('@/views/system/roles/index.vue'),
            meta: { title: '角色管理' }
          }
        ]
      },
      // 预算管理
      {
        path: '/budget',
        name: 'Budget',
        meta: { title: '预算管理' },
        children: [
          {
            path: '/budget/schemes',
            name: 'BudgetSchemes',
            component: () => import('@/views/budget/schemes/index.vue'),
            meta: { title: '预算方案' }
          },
          {
            path: '/budget/subjects',
            name: 'BudgetSubjects',
            component: () => import('@/views/budget/subjects/index.vue'),
            meta: { title: '预算科目' }
          },
          {
            path: '/budget/items',
            name: 'BudgetItems',
            component: () => import('@/views/budget/items/index.vue'),
            meta: { title: '预算明细' }
          }
        ]
      },
      // 支出控制
      {
        path: '/expense',
        name: 'Expense',
        meta: { title: '支出控制' },
        children: [
          {
            path: '/expense/pre-applications',
            name: 'PreApplications',
            component: () => import('@/views/expense/pre-applications/index.vue'),
            meta: { title: '事前申请' }
          },
          {
            path: '/expense/applications',
            name: 'ExpenseApplications',
            component: () => import('@/views/expense/applications/index.vue'),
            meta: { title: '费用报销' }
          },
          {
            path: '/expense/approvals',
            name: 'Approvals',
            component: () => import('@/views/expense/approvals/index.vue'),
            meta: { title: '审批工作台' }
          },
          {
            path: '/expense/payments',
            name: 'Payments',
            component: () => import('@/views/expense/payments/index.vue'),
            meta: { title: '付款管理' }
          }
        ]
      },
      // 采购管理
      {
        path: '/procurement',
        name: 'Procurement',
        meta: { title: '采购管理' },
        children: [
          {
            path: '/procurement/suppliers',
            name: 'Suppliers',
            component: () => import('@/views/procurement/suppliers/index.vue'),
            meta: { title: '供应商管理' }
          },
          {
            path: '/procurement/requisitions',
            name: 'PurchaseRequisitions',
            component: () => import('@/views/procurement/requisitions/index.vue'),
            meta: { title: '采购申请' }
          }
        ]
      },
      // 合同管理
      {
        path: '/contract',
        name: 'Contract',
        meta: { title: '合同管理' },
        children: [
          {
            path: '/contract/contracts',
            name: 'Contracts',
            component: () => import('@/views/contract/contracts/index.vue'),
            meta: { title: '合同台账' }
          },
          {
            path: '/contract/payment-schedules',
            name: 'PaymentSchedules',
            component: () => import('@/views/contract/payment-schedules/index.vue'),
            meta: { title: '付款计划' }
          }
        ]
      },
      // 资产管理
      {
        path: '/asset',
        name: 'Asset',
        meta: { title: '资产管理' },
        children: [
          {
            path: '/asset/categories',
            name: 'AssetCategories',
            component: () => import('@/views/asset/categories/index.vue'),
            meta: { title: '资产分类' }
          },
          {
            path: '/asset/assets',
            name: 'Assets',
            component: () => import('@/views/asset/assets/index.vue'),
            meta: { title: '资产台账' }
          }
        ]
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  
  if (to.meta.requiresAuth && !token) {
    next('/login')
  } else if (to.path === '/login' && token) {
    next('/dashboard')
  } else {
    next()
  }
})

export default router
