package models

import (
	"github.com/google/uuid"
)

// Department 科室/部门表 - 对应 tbl_departments
type Department struct {
	BaseModel
	ParentID    *uuid.UUID `json:"parent_id,omitempty" gorm:"type:uuid;index"`
	Name        string     `json:"name" gorm:"type:varchar(100);not null" validate:"required,max=100"`
	Code        *string    `json:"code,omitempty" gorm:"type:varchar(50);unique"`
	Level       *int64     `json:"level,omitempty" gorm:"default:1"`
	SortOrder   *int64     `json:"sort_order,omitempty" gorm:"default:0"`
	Description *string    `json:"description,omitempty" gorm:"type:text"`
	Status      *int64     `json:"status,omitempty" gorm:"default:1"`
	ManagerID   *uuid.UUID `json:"manager_id,omitempty" gorm:"type:uuid"`

	// 关联关系
	Parent   *Department   `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	Children []Department  `json:"children,omitempty" gorm:"foreignKey:ParentID"`
	Manager  *User         `json:"manager,omitempty" gorm:"foreignKey:ManagerID"`
	Users    []User        `json:"users,omitempty" gorm:"foreignKey:DepartmentID"`
}

// TableName 指定表名
func (Department) TableName() string {
	return "tbl_departments"
}
