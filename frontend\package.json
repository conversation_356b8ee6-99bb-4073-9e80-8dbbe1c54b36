{"name": "hospital-management-frontend", "version": "1.0.0", "description": "医院内部控制与运营管理系统前端", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "ant-design-vue": "^4.0.0", "@ant-design/icons-vue": "^7.0.1", "axios": "^1.5.0", "dayjs": "^1.11.9", "lodash-es": "^4.17.21", "@vueuse/core": "^10.4.1"}, "devDependencies": {"@types/node": "^20.5.9", "@types/lodash-es": "^4.17.9", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "@vitejs/plugin-vue": "^4.3.4", "@vue/eslint-config-typescript": "^12.0.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "typescript": "^5.2.2", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.2", "vite": "^4.4.9", "vue-tsc": "^1.8.8"}}