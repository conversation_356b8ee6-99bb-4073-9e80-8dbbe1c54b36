package models

import (
	"time"

	"github.com/google/uuid"
)

// ApprovalFlow 审批流实例表 - 对应 tbl_approval_flows
type ApprovalFlow struct {
	BaseModel
	BusinessID   uuid.UUID `json:"business_id" gorm:"type:uuid;not null" validate:"required"`
	BusinessType string    `json:"business_type" gorm:"type:varchar(50);not null" validate:"required,max=50"`
	Status       string    `json:"status" gorm:"type:varchar(30);not null" validate:"required,max=30"`
	CurrentStep  *int      `json:"current_step,omitempty" gorm:"default:0"`

	// 关联关系
	Nodes []ApprovalNode `json:"nodes,omitempty" gorm:"foreignKey:FlowID"`
}

// TableName 指定表名
func (ApprovalFlow) TableName() string {
	return "tbl_approval_flows"
}

// ApprovalNode 审批节点记录表 - 对应 tbl_approval_nodes
type ApprovalNode struct {
	ID          uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	FlowID      uuid.UUID  `json:"flow_id" gorm:"type:uuid;not null" validate:"required"`
	ApproverID  uuid.UUID  `json:"approver_id" gorm:"type:uuid;not null" validate:"required"`
	Status      string     `json:"status" gorm:"type:varchar(30);not null" validate:"required,max=30"`
	Comment     *string    `json:"comment,omitempty" gorm:"type:text"`
	Step        *int       `json:"step,omitempty"`
	ProcessedAt *time.Time `json:"processed_at,omitempty" gorm:"type:timestamptz"`
	CreatedAt   time.Time  `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt   time.Time  `json:"updated_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	DeletedAt   *time.Time `json:"deleted_at,omitempty" gorm:"type:timestamptz"`

	// 关联关系
	Flow     *ApprovalFlow `json:"flow,omitempty" gorm:"foreignKey:FlowID"`
	Approver *User         `json:"approver,omitempty" gorm:"foreignKey:ApproverID"`
}

// TableName 指定表名
func (ApprovalNode) TableName() string {
	return "tbl_approval_nodes"
}
