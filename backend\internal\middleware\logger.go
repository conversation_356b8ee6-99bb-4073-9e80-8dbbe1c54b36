package middleware

import (
	"github.com/gin-gonic/gin"
	"hospital-management/pkg/logger"
)

// Logger 日志中间件
func Logger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		logger.Info(
			"method:", param.Method,
			"path:", param.Path,
			"status:", param.StatusCode,
			"latency:", param.Latency,
			"client_ip:", param.ClientIP,
			"user_agent:", param.Request.UserAgent(),
		)
		return ""
	})
}

// Recovery 恢复中间件
func Recovery() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		logger.Error("Panic recovered:", recovered)
		c.JSON(500, gin.H{
			"error": "Internal server error",
		})
	})
}
