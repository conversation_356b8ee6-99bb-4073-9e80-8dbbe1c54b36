package models

import (
	"github.com/google/uuid"
)

// File 文件管理表 - 对应 tbl_files
type File struct {
	BaseModel
	FileName     string     `json:"file_name" gorm:"type:varchar(255);not null" validate:"required,max=255"`
	FilePath     string     `json:"file_path" gorm:"type:varchar(500);not null" validate:"required,max=500"`
	FileSize     *int64     `json:"file_size,omitempty"`
	MimeType     *string    `json:"mime_type,omitempty" gorm:"type:varchar(100)"`
	StorageType  string     `json:"storage_type" gorm:"type:varchar(20);not null;default:'LOCAL'" validate:"required"`
	BusinessID   *uuid.UUID `json:"business_id,omitempty" gorm:"type:uuid"`
	BusinessType *string    `json:"business_type,omitempty" gorm:"type:varchar(50)"`
}

// TableName 指定表名
func (File) TableName() string {
	return "tbl_files"
}
